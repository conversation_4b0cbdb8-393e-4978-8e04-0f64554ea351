# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Language Processing
- 英文思考,中文回答 (Think in English, respond in Chinese)

## Build Commands

### Dependencies
- OpenCV 4.x
- Eigen3: `sudo apt-get install libeigen3-dev`
- ONNXRuntime (download and extract, no compilation needed)

### Build Process
```bash
mkdir build && cd build
cmake ..
make
```

### Configuration Requirements
1. Update ONNXRUNTIME_DIR in CMakeLists.txt:12 to your ONNXRuntime path
2. Configure model paths in `tracker/deepsort/include/dataType.h`:
   - k_feature_dim: Feature dimension (default: 512)
   - k_feature_model_path: Path to feature extraction ONNX model
   - k_detect_model_path: Path to YOLOv5 ONNX model

### Run
```bash
# Main executable (requires video file "1.mp4" and model files)
./DeepSORT

# Test detector only
./test_detector
```

## Code Architecture

### High-Level Structure
This is a multi-object tracking (MOT) system with two tracking algorithms:
- **DeepSORT**: Feature-based tracking with appearance model
- **ByteTrack**: IoU-based tracking without appearance model

### Core Components

#### 1. Detection Module (`detector/`)
- **YOLOv5Detector**: Object detection using ONNX model
- Outputs `detect_result` objects with classId, confidence, bounding box

#### 2. Tracking Modules (`tracker/`)

**DeepSORT (`tracker/deepsort/`)**:
- `tracker.h/cpp`: Main tracking logic with Kalman filter and Hungarian algorithm
- `FeatureTensor.h/cpp`: Feature extraction using ONNX model for appearance similarity
- `kalmanfilter.h/cpp`: State prediction and motion model
- `nn_matching.h/cpp`: Nearest neighbor distance metric for appearance matching
- `track.h/cpp`: Individual track state management
- `linear_assignment.h/cpp`: Hungarian algorithm for track-detection assignment

**ByteTrack (`tracker/bytetrack/`)**:
- `BYTETracker.h/cpp`: IoU-based tracking without appearance features
- `STrack.h/cpp`: Track state for ByteTrack
- `BytekalmanFilter.h/cpp`: Kalman filter for ByteTrack

#### 3. Data Types (`tracker/deepsort/include/dataType.h`)
- Eigen matrix typedefs for detection boxes, features, Kalman filter states
- Configuration constants for model paths and feature dimensions

### Main Processing Flow
1. **Detection**: YOLOv5 detects objects in frame
2. **Feature Extraction** (DeepSORT only): Extract appearance features for detected persons
3. **Tracking**: 
   - DeepSORT: Combines motion (Kalman) + appearance (CNN features) + Hungarian assignment
   - ByteTrack: Uses only motion and IoU for assignment
4. **Visualization**: Draw bounding boxes with track IDs

### Key Configuration
- Only tracks person class (classId == 0)
- DeepSORT parameters: max_cosine_distance=0.2, nn_budget=100
- Models require: YOLOv5 ONNX model + feature extraction ONNX model (DeepSORT only)