# DeepSORT项目新人上手指南

## 项目概述

这是一个基于C++的多目标跟踪（MOT）系统，支持两种跟踪算法：
- **DeepSORT**: 基于外观特征的跟踪，结合深度学习特征提取
- **ByteTrack**: 基于IoU的跟踪，不使用外观特征

## 开发环境要求

### 操作系统
- Ubuntu 18.04 或更高版本
- Linux系统（已在Ubuntu上测试）

### 编译工具
- CMake 3.5+
- GCC支持C++11标准
- Make构建工具

## 依赖库安装

### 1. OpenCV 4.x
```bash
# 下载并编译安装OpenCV 4.6
wget https://github.com/opencv/opencv/archive/4.6.0.zip
unzip opencv-4.6.0.zip
cd opencv-4.6.0
mkdir build && cd build
cmake ..
make -j4
sudo make install
```

### 2. Eigen3
```bash
sudo apt-get install libeigen3-dev
```

### 3. ONNXRuntime
```bash
# 下载预编译版本（推荐）
wget https://github.com/microsoft/onnxruntime/releases/download/v1.12.1/onnxruntime-linux-x64-1.12.1.tgz
tar -xzf onnxruntime-linux-x64-1.12.1.tgz
# 解压后记住路径，后续需要配置到CMakeLists.txt中
```

## 项目配置

### 1. 修改ONNXRuntime路径
编辑 `CMakeLists.txt` 第12行：
```cmake
set(ONNXRUNTIME_DIR "/your/path/to/onnxruntime-linux-x64-1.12.1")
```

### 2. 配置模型路径
编辑 `tracker/deepsort/include/dataType.h`：
```cpp
const int k_feature_dim=512;  // 特征维度
const std::string k_feature_model_path ="./feature.onnx";  // 特征提取模型
const std::string k_detect_model_path ="./yolov5s.onnx";   // YOLOv5检测模型
```

### 3. 准备模型文件
在项目根目录放置以下文件：
- `yolov5s.onnx` - YOLOv5目标检测模型
- `feature.onnx` - DeepSORT特征提取模型（仅DeepSORT需要）
- `coco_80_labels_list.txt` - COCO类别标签文件
- `1.mp4` - 测试视频文件

## 编译步骤

```bash
# 创建构建目录
mkdir build && cd build

# 配置项目
cmake ..

# 编译
make

# 如果编译成功，会生成两个可执行文件：
# - DeepSORT: 主程序（包含DeepSORT和ByteTrack）
# - test_detector: 检测器测试程序
```

## 运行程序

### 主程序
```bash
./DeepSORT
# 需要确保有以下文件：
# - 1.mp4 (视频文件)
# - yolov5s.onnx (检测模型)
# - feature.onnx (特征模型，DeepSORT需要)
# - coco_80_labels_list.txt (标签文件)
```

### 检测器测试
```bash
./test_detector
# 仅测试YOLO检测功能，不需要特征模型
```

## 项目结构详解

```
DeepSORT/
├── CMakeLists.txt          # 构建配置
├── main.cpp               # 主程序入口
├── test_detector.cpp      # 检测器测试程序
├── detector/              # 检测模块
│   └── YOLOv5/           
│       ├── include/YOLOv5Detector.h
│       └── src/YOLOv5Detector.cpp
└── tracker/              # 跟踪模块
    ├── deepsort/         # DeepSORT实现
    │   ├── include/      # 头文件
    │   │   ├── dataType.h         # 数据类型定义
    │   │   ├── tracker.h          # 主跟踪器
    │   │   ├── track.h            # 单个轨迹
    │   │   ├── FeatureTensor.h    # 特征提取
    │   │   ├── kalmanfilter.h     # 卡尔曼滤波
    │   │   ├── nn_matching.h      # 最近邻匹配
    │   │   └── linear_assignment.h # 匈牙利算法
    │   └── src/          # 源文件
    └── bytetrack/        # ByteTrack实现
        ├── include/      
        │   ├── BYTETracker.h      # ByteTrack主跟踪器
        │   ├── STrack.h           # 轨迹状态
        │   └── BytekalmanFilter.h # ByteTrack卡尔曼滤波
        └── src/
```

## 核心算法说明

### DeepSORT工作流程
1. **目标检测**: YOLOv5检测视频帧中的目标
2. **特征提取**: 对检测到的person类别提取深度特征
3. **状态预测**: 使用卡尔曼滤波预测轨迹状态
4. **数据关联**: 结合运动信息和外观特征进行匹配
5. **轨迹管理**: 创建、更新、删除轨迹

### ByteTrack工作流程
1. **目标检测**: YOLOv5检测视频帧中的目标
2. **状态预测**: 使用卡尔曼滤波预测轨迹状态
3. **IoU匹配**: 仅基于IoU进行轨迹-检测关联
4. **轨迹管理**: 管理轨迹生命周期

## 关键参数配置

### DeepSORT参数（main.cpp）
```cpp
const int nn_budget=100;           // 特征库大小
const float max_cosine_distance=0.2;  // 余弦距离阈值
```

### ByteTrack参数（main.cpp）
```cpp
int fps=20;                        // 视频帧率
BYTETracker bytetracker(fps, 30);  // 第二个参数是track_buffer
```

## 常见问题解决

### 1. 编译错误
- 确保所有依赖库已正确安装
- 检查CMakeLists.txt中的路径配置
- 确保编译器支持C++11

### 2. 运行时错误
- 检查模型文件是否存在且路径正确
- 确保视频文件存在且可读
- 检查ONNXRuntime库路径配置

### 3. 模型文件获取
- YOLOv5模型: https://github.com/ultralytics/yolov5
- 特征模型需要自行训练或从项目提供的网盘下载

## 代码修改指南

### 切换跟踪算法
在main.cpp中修改：
```cpp
// 使用DeepSORT
test_deepsort(frame, results, mytracker);

// 使用ByteTrack  
test_bytetrack(frame, results, bytetracker);
```

### 修改检测类别
当前只跟踪person类（classId == 0），如需跟踪其他类别：
```cpp
if(dr.classId == 0) // 修改这里的类别ID
```

### 调整输出格式
- 视频输出：修改main.cpp中的VideoWriter配置
- 显示窗口：修改cv::imshow相关代码
- 保存结果：可添加轨迹数据保存功能

## 性能优化建议

1. **模型选择**: 根据精度要求选择YOLOv5s/m/l/x
2. **特征维度**: 根据需求调整feature_dim（128/256/512）
3. **视频分辨率**: 降低输入分辨率可提升速度
4. **跟踪参数**: 调整nn_budget和max_cosine_distance

## 扩展开发

### 添加新的检测器
1. 在detector/目录创建新的检测器类
2. 实现统一的detect_result接口
3. 在main.cpp中集成

### 添加新的跟踪器
1. 在tracker/目录创建新的跟踪器类
2. 实现轨迹输出接口
3. 在main.cpp中添加测试函数

这个指南应该能帮助新人快速上手DeepSORT项目的开发和使用。